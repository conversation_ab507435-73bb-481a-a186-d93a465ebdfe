import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import { NUMBERS, UserStage } from 'src/constant/objects';
import { CommonService } from 'src/utils/comman.service';
import { PgService } from 'src/database/pg/pg.service';
import { registeredUsers } from 'src/database/pg/entities/registeredUsers';
import { RedisService } from 'src/database/redis/redis.service';
import {
  CHART_GRAPH_DATE_QUERY,
  FILTER_TYPES,
  QUERY_GRAPH_FILTERS,
  QUERY_GRAPHS,
} from 'src/constant/global';
import { ClickHouseService } from 'src/database/clickhouse/clickhouse.service';
@Injectable()
export class AnalyticsService {
  constructor(
    private readonly commonService: CommonService,
    private readonly pgService: PgService,
    private readonly redisService: RedisService,
    private readonly ClickHouseService: ClickHouseService,
  ) {}

  //#region Dashboard
  async getDashboard() {
    const [graphs, filters] = await Promise.all([
      this.ClickHouseService.injectQuery(QUERY_GRAPHS),
      this.ClickHouseService.injectQuery(QUERY_GRAPH_FILTERS),
    ]);

    const filtersArray = filters as any[];
    const today = new Date().toISOString().split('T')[0];
    const updatedFilters = filtersArray.map((filter) => {
      if (filter.type === FILTER_TYPES.DATE) {
        return {
          ...filter,
          defaultValue: {
            start: today,
            end: today,
          },
        };
      }
      return filter;
    });
    return { filters: updatedFilters, graphs };
  }
  //#endregion

  //#region User Analytics
  async userAnalytics(query) {
    const cacheKey = this.createDynamicRedisKey(query, 'ANALYTICS');
    const today = new Date().toISOString().slice(0, 10);
    const queryDate = query.startDate;
    const isNotToday = queryDate !== today;

    // Check ClickHouse
    if (isNotToday) {
      const clickHouse = CHART_GRAPH_DATE_QUERY.replace(
        '{{TABLE_NAME}}',
        'UsersStageAnalytics',
      ).replace('{{GRAPH_DATE}}', queryDate);
      const clickHouseData =
        await this.ClickHouseService.injectQuery(clickHouse);
      const row = clickHouseData[0];

      return Object.keys(UserStage).map((stageKey) => ({
        xKey: this.commonService.toPascalCase(stageKey),
        yKey: Number(row[stageKey.toLowerCase()]) || 0,
      }));
    } else {
      const cachedData = await this.redisService.get(cacheKey);
      if (cachedData) {
        return JSON.parse(cachedData).result;
      }
    }

    // Fetch fresh data from PostgreSQL with applied filters
    const where = await this.buildWhereFilter(query);
    const stageRows = await this.pgService.findAll(registeredUsers, {
      attributes: ['stage', 'gender', 'typeOfDevice', 'completedLoans'],
      where,
      raw: true,
    });

    // Initialize stage and process stage data
    const stage = {};
    for (const key in UserStage) stage[key] = 0;

    for (const row of stageRows) {
      const stageKey = Object.keys(UserStage).find(
        (k) => UserStage[k] == row.stage,
      );
      if (stageKey) stage[stageKey]++;
    }

    const stageSummary = Object.entries(stage).map(([xKey, yKey]) => ({
      xKey: this.commonService.toPascalCase(xKey),
      yKey,
    }));

    // Initialize registration
    let registrationCount = 0,
      male = 0,
      female = 0,
      android = 0,
      ios = 0,
      web = 0,
      newUsers = 0,
      repeatUsers = 0;

    // Process registration analytics
    for (const { gender, typeOfDevice, completedLoans } of stageRows) {
      registrationCount++;

      if (gender === 'MALE') male++;
      else if (gender === 'FEMALE') female++;

      if (typeOfDevice === '0') android++;
      else if (typeOfDevice === '1') ios++;
      else if (typeOfDevice === '2') web++;

      if (completedLoans === 0) newUsers++;
      else if (completedLoans > 0) repeatUsers++;
    }

    const registrationSummary = [
      { xKey: 'Registration Count', yKey: registrationCount },
      { xKey: 'Male Registration', yKey: male },
      { xKey: 'Female Registration', yKey: female },
      { xKey: 'Android Users', yKey: android },
      { xKey: 'IOS Users', yKey: ios },
      { xKey: 'Web Users', yKey: web },
      { xKey: 'New Users', yKey: newUsers },
      { xKey: 'Repeat Users', yKey: repeatUsers },
    ];

    const result = { stageSummary, registrationSummary };

    if (isNotToday) {
      const rowObj = {
        graph_date: queryDate,
        ...result.stageSummary.reduce((acc, row) => {
          acc[row.xKey.replace(/\s+/g, '_').toLowerCase()] = row.yKey;
          return acc;
        }, {}),
      };
      await this.ClickHouseService.insertToClickhouse(
        'UsersStageAnalytics',
        rowObj,
      );
    } else {
      await this.redisService.set(
        cacheKey,
        JSON.stringify({ result }),
        NUMBERS.THREE_HOURS_IN_SECONDS,
      );
    }

    return result;
  }
  //#endregion

  // stage summary
  async getUserStage(query) {
    try {
      // First try to get data from ClickHouse
      const clickHouseData = await this.getClickHouseStageData(query);
      if (clickHouseData) {
        return clickHouseData;
      }

      // Fallback to PostgreSQL
      const postgresData = await this.getPostgreSQLStageData(query);
      return postgresData;
    } catch (error) {
      console.error('Error in getUserStage:', error);
      // Fallback to PostgreSQL on error
      return await this.getPostgreSQLStageData(query);
    }
  }

  // registration summary
  async getUserStageCount(query) {
    try {
      // First try to get data from ClickHouse
      const clickHouseData = await this.getClickHouseRegistrationData(query);
      if (clickHouseData) {
        return clickHouseData;
      }

      // Fallback to PostgreSQL
      const postgresData = await this.getPostgreSQLRegistrationData(query);
      return postgresData;
    } catch (error) {
      console.error('Error in getUserStageCount:', error);
      // Fallback to PostgreSQL on error
      return await this.getPostgreSQLRegistrationData(query);
    }
  }

  //#region ClickHouse Data Methods
  private async getClickHouseStageData(query) {
    const { startDate, endDate } = query;

    try {
      const usersStageData = await this.ClickHouseService.injectQuery(
        `SELECT * FROM UsersStageAnalytics WHERE graph_date BETWEEN toDate('${startDate}') AND toDate('${endDate}')`,
      );

      if (
        usersStageData &&
        Array.isArray(usersStageData) &&
        usersStageData.length > 0
      ) {
        return this.processClickHouseStageData(usersStageData);
      }

      // Try RegisteredUsers table
      const registeredUsersData = await this.ClickHouseService.injectQuery(
        `SELECT * FROM RegisteredUsers WHERE graph_date BETWEEN toDate('${startDate}') AND toDate('${endDate}')`,
      );

      if (
        registeredUsersData &&
        Array.isArray(registeredUsersData) &&
        registeredUsersData.length > 0
      ) {
        return this.processStageDataFromRegisteredUsers(registeredUsersData);
      }

      return null;
    } catch (error) {
      console.error('ClickHouse stage data error:', error);
      return null;
    }
  }

  private async getClickHouseRegistrationData(query) {
    const { startDate, endDate } = query;

    try {
      const registeredUsersData = await this.ClickHouseService.injectQuery(
        `SELECT * FROM RegisteredUsers WHERE graph_date BETWEEN toDate('${startDate}') AND toDate('${endDate}')`,
      );

      if (
        registeredUsersData &&
        Array.isArray(registeredUsersData) &&
        registeredUsersData.length > 0
      ) {
        return this.processRegistrationDataFromClickHouse(registeredUsersData);
      }

      return null;
    } catch (error) {
      console.error('ClickHouse registration data error:', error);
      return null;
    }
  }

  //#region Data Processing Methods
  private processClickHouseStageData(usersStageData: any[]) {
    // Aggregate data from multiple days
    const aggregatedStage = {};
    for (const key in UserStage) {
      aggregatedStage[key] = 0;
    }

    // Sum up data from all days
    for (const row of usersStageData) {
      for (const stageKey of Object.keys(UserStage)) {
        const value = Number(row[stageKey.toLowerCase()]) || 0;
        aggregatedStage[stageKey] += value;
      }
    }

    // Convert to required format
    return Object.entries(aggregatedStage).map(([xKey, yKey]) => ({
      xKey: this.commonService.toPascalCase(xKey),
      yKey,
    }));
  }

  private processStageDataFromRegisteredUsers(registeredUsersData: any[]) {
    // Initialize stage counters
    const stage = {};
    for (const key in UserStage) {
      stage[key] = 0;
    }

    // Count stages
    for (const row of registeredUsersData) {
      const stageKey = Object.keys(UserStage).find(
        (k) => UserStage[k] == row.stage,
      );
      if (stageKey) {
        stage[stageKey]++;
      }
    }

    // Convert to required format
    return Object.entries(stage).map(([xKey, yKey]) => ({
      xKey: this.commonService.toPascalCase(xKey),
      yKey,
    }));
  }

  private processRegistrationDataFromClickHouse(registeredUsersData: any[]) {
    const analytics = {
      registrationCount: 0,
      male: 0,
      female: 0,
      android: 0,
      ios: 0,
      web: 0,
      newUsers: 0,
      repeatUsers: 0,
    };

    // Process each row
    for (const {
      gender,
      typeOfDevice,
      completedLoans,
    } of registeredUsersData) {
      analytics.registrationCount++;

      // Gender analytics
      if (gender === 'MALE') analytics.male++;
      else if (gender === 'FEMALE') analytics.female++;

      // Device analytics
      if (typeOfDevice === '0') analytics.android++;
      else if (typeOfDevice === '1') analytics.ios++;
      else if (typeOfDevice === '2') analytics.web++;

      // User type analytics
      if (completedLoans === 0) analytics.newUsers++;
      else if (completedLoans > 0) analytics.repeatUsers++;
    }

    // Convert to required format
    return [
      { xKey: 'Registration Count', yKey: analytics.registrationCount },
      { xKey: 'Male Registration', yKey: analytics.male },
      { xKey: 'Female Registration', yKey: analytics.female },
      { xKey: 'Android Users', yKey: analytics.android },
      { xKey: 'IOS Users', yKey: analytics.ios },
      { xKey: 'Web Users', yKey: analytics.web },
      { xKey: 'New Users', yKey: analytics.newUsers },
      { xKey: 'Repeat Users', yKey: analytics.repeatUsers },
    ];
  }
  //#endregion

  //#region PostgreSQL Fallback Methods
  private async getPostgreSQLStageData(query) {
    const where = await this.buildWhereFilter(query);
    const stageRows = await this.pgService.findAll(registeredUsers, {
      attributes: ['stage'],
      where,
      raw: true,
    });

    // Initialize stage counters
    const stage = {};
    for (const key in UserStage) {
      stage[key] = 0;
    }

    // Count stages
    for (const row of stageRows) {
      const stageKey = Object.keys(UserStage).find(
        (k) => UserStage[k] == row.stage,
      );
      if (stageKey) {
        stage[stageKey]++;
      }
    }

    // Convert to required format
    return Object.entries(stage).map(([xKey, yKey]) => ({
      xKey: this.commonService.toPascalCase(xKey),
      yKey,
    }));
  }

  private async getPostgreSQLRegistrationData(query) {
    const where = await this.buildWhereFilter(query);
    const stageRows = await this.pgService.findAll(registeredUsers, {
      attributes: ['gender', 'typeOfDevice', 'completedLoans'],
      where,
      raw: true,
    });

    const analytics = {
      registrationCount: 0,
      male: 0,
      female: 0,
      android: 0,
      ios: 0,
      web: 0,
      newUsers: 0,
      repeatUsers: 0,
    };

    // Process each row
    for (const { gender, typeOfDevice, completedLoans } of stageRows) {
      analytics.registrationCount++;

      // Gender analytics
      if (gender === 'MALE') analytics.male++;
      else if (gender === 'FEMALE') analytics.female++;

      // Device analytics
      if (typeOfDevice === '0') analytics.android++;
      else if (typeOfDevice === '1') analytics.ios++;
      else if (typeOfDevice === '2') analytics.web++;

      // User type analytics
      if (completedLoans === 0) analytics.newUsers++;
      else if (completedLoans > 0) analytics.repeatUsers++;
    }

    // Convert to required format
    return [
      { xKey: 'Registration Count', yKey: analytics.registrationCount },
      { xKey: 'Male Registration', yKey: analytics.male },
      { xKey: 'Female Registration', yKey: analytics.female },
      { xKey: 'Android Users', yKey: analytics.android },
      { xKey: 'IOS Users', yKey: analytics.ios },
      { xKey: 'Web Users', yKey: analytics.web },
      { xKey: 'New Users', yKey: analytics.newUsers },
      { xKey: 'Repeat Users', yKey: analytics.repeatUsers },
    ];
  }
  //#endregion
  //#endregion

  //#region Filter Analytics
  private async buildWhereFilter(query) {
    const { startDate, endDate } = this.commonService.getUTCDateRange(
      query.startDate,
      query.endDate,
    );
    const where: any = {};

    // Basic filters
    if (startDate && endDate) {
      where.createdAt = { [Op.gte]: startDate, [Op.lte]: endDate };
    }
    if (query.gender) where.gender = query.gender;
    if (query.state) where.state = query.state;

    let userIds: string[] | null = null;

    // CIBIL/PL/Salary filter
    if (
      query?.startCibil ||
      query?.endCibil ||
      query?.startPl ||
      query?.endPl ||
      query?.startSalary ||
      query?.endSalary
    ) {
      const scoreWhere: any = {};

      if (query?.startCibil || query?.endCibil) {
        scoreWhere.cibilScore = {};
        if (query?.startCibil) scoreWhere.cibilScore[Op.gte] = query.startCibil;
        if (query?.endCibil) scoreWhere.cibilScore[Op.lte] = query.endCibil;
      }
      if (query?.startPl || query?.endPl) {
        scoreWhere.plScore = {};
        if (query?.startPl) scoreWhere.plScore[Op.gte] = query.startPl;
        if (query?.endPl) scoreWhere.plScore[Op.lte] = query.endPl;
      }
      if (query?.startSalary || query?.endSalary) {
        scoreWhere.monthlyIncome = {};
        if (query?.startSalary)
          scoreWhere.monthlyIncome[Op.gte] = query.startSalary;
        if (query?.endSalary)
          scoreWhere.monthlyIncome[Op.lte] = query.endSalary;
      }

      const cibilUsers = await this.pgService.findAll('CibilScoreEntity', {
        attributes: ['userId'],
        where: scoreWhere,
        raw: true,
      });

      userIds = cibilUsers.map((u) => u.userId);
    }

    // Age filter
    if (query?.startAge || query?.endAge) {
      const kycUsers = await this.pgService.findAll('KYCEntity', {
        attributes: ['userId', 'aadhaarDOB'],
        where: { aadhaarDOB: { [Op.ne]: null, [Op.not]: '' } },
        raw: true,
      });

      const currentYear = new Date().getFullYear();
      const ageUserIds = kycUsers
        .filter(({ aadhaarDOB }) => {
          let year: number | null = null;
          if (/^\d{2}\/\d{2}\/\d{4}$/.test(aadhaarDOB))
            year = parseInt(aadhaarDOB.split('/')[2], 10);
          else if (/^\d{2}-\d{2}-\d{4}$/.test(aadhaarDOB))
            year = parseInt(aadhaarDOB.split('-')[2], 10);
          else if (/^\d{4}-\d{2}-\d{2}$/.test(aadhaarDOB))
            year = parseInt(aadhaarDOB.split('-')[0], 10);

          if (!year) return false;
          const age = currentYear - year;
          if (query?.startAge && age < query.startAge) return false;
          if (query?.endAge && age > query.endAge) return false;
          return true;
        })
        .map((u) => u.userId);

      userIds = userIds
        ? userIds.filter((id) => ageUserIds.includes(id))
        : ageUserIds;
    }

    if (userIds !== null) {
      where.id = userIds.length > 0 ? { [Op.in]: userIds } : null;
    }

    return where;
  }
  //#endregion

  //#region Dynamic Redis Key
  private createDynamicRedisKey(query, baseKey: string): string {
    const normalized = {
      gender: query.gender ? query?.gender.toUpperCase() : null,
      state: query.state ? query?.state.toUpperCase() : null,
      age: {
        start: query?.startAge ?? '',
        end: query?.endAge ?? '',
      },
      cibil: {
        start: query?.startCibil ?? '',
        end: query?.endCibil ?? '',
      },
      pl: {
        start: query?.startPl ?? '',
        end: query?.endPl ?? '',
      },
      salary: {
        start: query?.startSalary ?? '',
        end: query?.endSalary ?? '',
      },
    };
    return `${baseKey}:${JSON.stringify(normalized)}`;
  }
  //#endregion
}
